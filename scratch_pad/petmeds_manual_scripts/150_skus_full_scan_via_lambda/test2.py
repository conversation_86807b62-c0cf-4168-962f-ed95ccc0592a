import os
import json
import time
from urllib.parse import urljoin, urlparse
from openai import OpenAI
from browserbase import Browserbase
from playwright.sync_api import sync_playwright
from dotenv import load_dotenv


api_key = 'bb_live_R8p3eOamO4U9QIRIiItW_0ztWoM'
project_id = 'b4e9ae7f-7312-4b58-bb9e-684c436effcf'

# --- Configuration ---
load_dotenv()
browserbase = Browserbase(api_key=api_key)
# Initialize the OpenAI client
client = OpenAI(api_key='********************************************************************************************************************************************************************')


def get_page_html(url: str) -> str:
    """Uses Browserbase to load a URL and return its full HTML content."""
    print(f"Crawling {url} with Browserbase...")
    try:
        with sync_playwright() as playwright:
            # Create a session on Browserbase
            session = browserbase.sessions.create(project_id=project_id)

            # Connect to the remote session
            chromium = playwright.chromium
            browser = chromium.connect_over_cdp(session.connect_url)
            context = browser.contexts[0]
            page = context.pages[0]

            try:
                # Navigate to the URL and get HTML content
                page.goto(url, wait_until="load")
                html_content = page.content()
                print("Page loaded successfully.")
                return html_content
            finally:
                page.close()
                browser.close()
    except Exception as e:
        print(f"Error crawling {url}: {e}")
        return ""


def truncate_html_content(html_content: str, max_chars: int = 50000) -> str:
    """Truncate HTML content to fit within token limits while preserving structure."""
    if len(html_content) <= max_chars:
        return html_content

    # Try to find a good truncation point (end of a tag)
    truncated = html_content[:max_chars]
    last_tag_end = truncated.rfind('>')
    if last_tag_end > max_chars * 0.8:  # If we found a tag end in the last 20%
        truncated = truncated[:last_tag_end + 1]

    return truncated + "\n<!-- Content truncated for token limit -->"


def analyze_html_with_openai(html_content: str, prompt: str) -> dict:
    """Sends HTML content and a prompt to OpenAI for analysis."""
    if not html_content:
        return {"error": "No HTML content provided."}

    # Truncate HTML content to avoid token limit issues
    truncated_html = truncate_html_content(html_content)
    print(f"Analyzing HTML with OpenAI (gpt-4o)... (HTML length: {len(truncated_html)} chars)")

    try:
        # Use OpenAI's ChatCompletions endpoint with JSON Mode
        response = client.chat.completions.create(
            # Using gpt-4o is recommended for its strong reasoning and JSON capabilities
            model="gpt-4o",
            # This is the crucial part for getting reliable JSON output
            response_format={"type": "json_object"},
            messages=[
                {
                    "role": "system",
                    "content": "You are an expert e-commerce taxonomist designed to output structured JSON data."
                },
                {
                    "role": "user",
                    "content": f"{prompt}\n\nHere is the HTML content to analyze:\n\n{truncated_html}"
                }
            ],
            temperature=0.1,
            max_tokens=4096,  # Increased from default to handle potentially large HTML
        )

        response_content = response.choices[0].message.content
        # The response content is a JSON string, so we parse it
        return json.loads(response_content)

    except Exception as e:
        print(f"Error analyzing with OpenAI: {e}")
        return {"error": str(e)}


def extract_navigation_links(url: str) -> dict:
    """Extract navigation links and their URLs from a page."""
    html = get_page_html(url)

    prompt = """
    Analyze the provided HTML and extract all navigation links with their URLs.

    Structure your output as a JSON object with the following format:
    {
      "categories": [
        {
          "name": "Category Name",
          "url": "relative or absolute URL",
          "level": 1
        }
      ]
    }

    Focus on:
    1. Main navigation menu items (clothing, shoes, accessories, etc.)
    2. Sub-category links within dropdowns or menus
    3. Include both the text and href attributes
    4. Determine the hierarchy level (1 for main categories, 2 for subcategories, etc.)
    5. Only include product category links, not promotional or utility links

    Produce only the valid JSON object in your response, nothing else.
    """

    return analyze_html_with_openai(html, prompt)


def get_main_navigation_taxonomy(url: str):
    """Part 1: Extracts the main navigation hierarchy from the main page."""
    html = get_page_html(url)

    prompt = """
    Analyze the provided HTML from the Debenhams women's landing page.
    Your task is to extract the complete navigation structure with URLs.

    Structure your output as a single JSON object with the following format:
    {
      "product_hierarchy": {
        "Category Name": {
          "url": "category_url",
          "subcategories": {
            "Subcategory Name": {
              "url": "subcategory_url",
              "subcategories": {}
            }
          }
        }
      },
      "curated_navigation": {
        "Section Name": {
          "url": "section_url"
        }
      }
    }

    1.  For "product_hierarchy":
        -   Identify the primary product categories (L1), such as Clothing, Shoes, Accessories.
        -   For each category, include its URL and any visible subcategories
        -   Include specialist sizing categories like Plus Size, Petite, and Tall.

    2.  For "curated_navigation":
        -   Identify thematic or event-based navigation links with their URLs
        -   Examples include "New In", "The Holiday Shop", "Wedding Guest", "Workwear", "Sale".

    Produce only the valid JSON object in your response, nothing else.
    """

    return analyze_html_with_openai(html, prompt)


def normalize_url(base_url: str, url: str) -> str:
    """Normalize a URL to be absolute."""
    if url.startswith('http'):
        return url
    return urljoin(base_url, url)


def is_valid_category_url(url: str, base_domain: str) -> bool:
    """Check if URL is a valid category URL to crawl."""
    parsed = urlparse(url)

    # Must be same domain
    if base_domain not in parsed.netloc and parsed.netloc != '':
        return False

    # Skip certain patterns
    skip_patterns = [
        '/search', '/account', '/checkout', '/cart', '/login', '/register',
        '/help', '/contact', '/about', '/terms', '/privacy', '/blog',
        '.pdf', '.jpg', '.png', '.gif', '.css', '.js', '#', 'javascript:',
        '/sale', '/offers', '/new-in', '/brands', '/wk', '/extra-promo',
        '/trending-deals', '/spend-and-save', '/hotlist', '/beauty-skin-care',
        '/home-sale', '/home-electricals', '/categories/wk', '/categories/debenhams-beauty'
    ]

    # Only allow specific category patterns
    valid_patterns = [
        '/clothing', '/shoes', '/accessories', '/bags', '/jewellery',
        '/category/', '/categories/clothing', '/categories/shoes',
        '/categories/accessories', '/categories/bags', '/categories/jewellery'
    ]

    # Check if URL contains valid patterns
    has_valid_pattern = any(pattern in url.lower() for pattern in valid_patterns)

    # Check if URL contains skip patterns
    has_skip_pattern = any(pattern in url.lower() for pattern in skip_patterns)

    return has_valid_pattern and not has_skip_pattern


def extract_subcategories_from_page(url: str, base_url: str) -> dict:
    """Extract subcategories and their URLs from a category page."""
    html = get_page_html(url)

    prompt = """
    Analyze the provided HTML from a category page and extract ONLY product subcategory navigation links.

    Structure your output as a JSON object:
    {
      "subcategories": {
        "Subcategory Name": {
          "url": "subcategory_url",
          "is_leaf": true/false
        }
      },
      "filters": {
        "Filter Group": ["option1", "option2"]
      }
    }

    For subcategories:
    - ONLY include actual product categories (e.g., "Dresses", "Tops", "Boots", "Handbags")
    - EXCLUDE promotional links, sales, brands, or marketing content
    - EXCLUDE URLs containing: "sale", "offer", "promo", "brand", "new-in", "trending"
    - Look for navigation menus, category grids, or product type filters
    - Set "is_leaf" to true if this is a final product listing page with individual products

    For filters:
    - Extract filter groups like Size, Color, Brand, Price, etc. from filter panels
    - Include all available options for each filter group

    Focus on product taxonomy, not promotional content. Produce only the valid JSON object.
    """

    return analyze_html_with_openai(html, prompt)


def crawl_taxonomy_recursively(taxonomy: dict, base_url: str, visited_urls: set, max_depth: int = 4, current_depth: int = 0) -> dict:
    """Recursively crawl through taxonomy to build complete hierarchy."""

    if current_depth >= max_depth:
        print(f"Reached maximum depth {max_depth}, stopping recursion")
        return taxonomy

    base_domain = urlparse(base_url).netloc

    def crawl_category(category_data: dict, category_name: str, depth: int) -> dict:
        if depth >= max_depth:
            return category_data

        if 'url' not in category_data:
            return category_data

        url = normalize_url(base_url, category_data['url'])

        if url in visited_urls or not is_valid_category_url(url, base_domain):
            return category_data

        print(f"{'  ' * depth}Crawling {category_name}: {url}")
        visited_urls.add(url)

        # Add delay to be respectful
        time.sleep(1)

        try:
            page_data = extract_subcategories_from_page(url, base_url)

            if 'subcategories' in page_data and page_data['subcategories']:
                if 'subcategories' not in category_data:
                    category_data['subcategories'] = {}

                for subcat_name, subcat_data in page_data['subcategories'].items():
                    if subcat_name not in category_data['subcategories']:
                        category_data['subcategories'][subcat_name] = subcat_data

                    # Recursively crawl subcategories
                    category_data['subcategories'][subcat_name] = crawl_category(
                        category_data['subcategories'][subcat_name],
                        subcat_name,
                        depth + 1
                    )

            # Add filters if found
            if 'filters' in page_data and page_data['filters']:
                category_data['filters'] = page_data['filters']

        except Exception as e:
            print(f"{'  ' * depth}Error crawling {category_name}: {e}")

        return category_data

    # Crawl product hierarchy
    if 'product_hierarchy' in taxonomy:
        for category_name, category_data in taxonomy['product_hierarchy'].items():
            print(f"\nCrawling main category: {category_name}")
            taxonomy['product_hierarchy'][category_name] = crawl_category(
                category_data, category_name, current_depth + 1
            )

    return taxonomy


def get_filtering_taxonomy(url: str):
    """Part 2: Extracts the filtering facets from a category page."""
    html = get_page_html(url)

    prompt = """
    Analyze the provided HTML from a product category page. Your goal is to identify all the filter groups (faceted search) and their available options.

    Structure your output as a single JSON object where:
    - Each key is the name of a filter group (e.g., "Size", "Colour", "Brand", "Fit", "Occasion").
    - The value for each key is an array of strings representing the available filter options within that group.

    For example:
    {
      "Size": ["6", "8", "10", "12"],
      "Colour": ["Black", "White", "Blue", "Red"],
      "Brand": ["Warehouse", "Oasis", "Coast"]
    }

    Focus only on the filter sidebar/panel. Produce only the valid JSON object in your response.
    """

    return analyze_html_with_openai(html, prompt)


def save_taxonomy_to_file(taxonomy: dict, filename: str):
    """Save taxonomy to a JSON file."""
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(taxonomy, f, indent=2, ensure_ascii=False)
    print(f"Taxonomy saved to {filename}")


if __name__ == "__main__":
    WOMENS_URL = "https://www.debenhams.com/pages/womens"
    BASE_URL = "https://www.debenhams.com"

    print("🚀 STARTING COMPLETE TAXONOMY EXTRACTION")
    print("=" * 60)

    print("\n📋 STEP 1: EXTRACTING INITIAL NAVIGATION STRUCTURE")
    print("-" * 50)
    main_taxonomy = get_main_navigation_taxonomy(WOMENS_URL)

    if 'error' in main_taxonomy:
        print(f"❌ Error in initial extraction: {main_taxonomy['error']}")
        exit(1)

    print("✅ Initial navigation structure extracted")
    print(f"Found {len(main_taxonomy.get('product_hierarchy', {}))} main categories")

    print("\n🔄 STEP 2: RECURSIVELY CRAWLING ALL CATEGORIES")
    print("-" * 50)
    visited_urls = set()

    try:
        complete_taxonomy = crawl_taxonomy_recursively(
            main_taxonomy,
            BASE_URL,
            visited_urls,
            max_depth=2  # Start with depth 2 to avoid too many requests
        )

        print(f"\n✅ CRAWLING COMPLETE!")
        print(f"📊 Total URLs visited: {len(visited_urls)}")

        # Save the complete taxonomy
        save_taxonomy_to_file(complete_taxonomy, 'complete_taxonomy.json')

        print("\n📋 FINAL TAXONOMY STRUCTURE:")
        print("=" * 60)
        print(json.dumps(complete_taxonomy, indent=2))

        # Print summary statistics
        print("\n📈 TAXONOMY STATISTICS:")
        print("-" * 30)

        def count_categories(data, level=0):
            count = 0
            if isinstance(data, dict):
                for key, value in data.items():
                    if key == 'subcategories' and isinstance(value, dict):
                        count += len(value)
                        for subcat in value.values():
                            count += count_categories(subcat, level + 1)
                    elif key == 'product_hierarchy' and isinstance(value, dict):
                        count += len(value)
                        for cat in value.values():
                            count += count_categories(cat, level + 1)
            return count

        total_categories = count_categories(complete_taxonomy)
        print(f"Total categories found: {total_categories}")
        print(f"URLs crawled: {len(visited_urls)}")

    except KeyboardInterrupt:
        print("\n⚠️  Crawling interrupted by user")
        print("Saving partial results...")
        save_taxonomy_to_file(main_taxonomy, 'partial_taxonomy.json')
    except Exception as e:
        print(f"\n❌ Error during crawling: {e}")
        print("Saving partial results...")
        save_taxonomy_to_file(main_taxonomy, 'partial_taxonomy.json')

    print("\n🎉 TAXONOMY EXTRACTION COMPLETE!")
    print("=" * 60)
