import os
import json
from browserbase import Browserbase
from playwright.sync_api import sync_playwright


def generate_chicos_taxonomy():
    """
    Crawls chicos.com using BrowserBase and <PERSON><PERSON> to extract the product taxonomy.
    """
    print("--- Starting <PERSON>'s taxonomy crawl ---")

    # Check for required environment variables
    api_key = 'bb_live_R8p3eOamO4U9QIRIiItW_0ztWoM'
    project_id = 'b4e9ae7f-7312-4b58-bb9e-684c436effcf'

    if not api_key:
        print("Error: BROWSERBASE_API_KEY environment variable is not set.")
        return

    if not project_id:
        print("Error: BROWSERBASE_PROJECT_ID environment variable is not set.")
        return

    try:
        # Initialize BrowserBase client
        client = Browserbase(api_key=api_key)
        print("BrowserBase client initialized successfully.")
    except Exception as e:
        print(f"Error: Could not initialize BrowserBase client: {e}")
        return

    # These are top-level categories we want to ignore as they are not standard product types.
    CATEGORY_BLACKLIST = ['NEW', 'COLLECTIONS', 'SALE']

    print("Creating BrowserBase session...")
    try:
        # Create a session on Browserbase
        session = client.sessions.create(project_id=project_id)
        print(f"Session created successfully: {session.id}")
    except Exception as e:
        print(f"Error creating BrowserBase session: {e}")
        return

    print("Connecting to remote browser and extracting data...")
    try:
        with sync_playwright() as playwright:
            # Connect to the remote session
            chromium = playwright.chromium
            browser = chromium.connect_over_cdp(session.connect_url)
            context = browser.contexts[0]
            page = context.pages[0]

            try:
                # Navigate to Chico's website
                print("Navigating to https://www.debenhams.com/pages/womens...")
                page.goto("https://www.debenhams.com/pages/womens", wait_until="domcontentloaded", timeout=60000)
                print("Page loaded successfully.")

                # Wait a bit for any dynamic content to load
                page.wait_for_timeout(3000)

                # Check what page we actually loaded
                current_url = page.url
                page_title = page.title()
                print(f"Current URL: {current_url}")
                print(f"Page title: {page_title}")

                # Extract the navigation data using JavaScript
                print("Extracting navigation data...")
                extracted_data = page.evaluate("""
                    () => {
                        const data = [];

                        // Find all top-level navigation items
                        const topLevelItems = document.querySelectorAll("nav[aria-label='Main Menu'] > ul > li.nav-item--primary");

                        topLevelItems.forEach(topItem => {
                            const level1Element = topItem.querySelector("a.nav-link--primary > span");
                            if (!level1Element) return;

                            const level1Name = level1Element.textContent?.trim() || "";
                            if (!level1Name) return;

                            const itemData = {
                                level1_name: level1Name,
                                level2_items: []
                            };

                            // Find Level 2 items
                            const level2Items = topItem.querySelectorAll("ul.nav-list--secondary > li.nav-item--secondary");

                            level2Items.forEach(level2Item => {
                                const level2Element = level2Item.querySelector("a.nav-link--secondary");
                                if (!level2Element) return;

                                const level2Name = level2Element.textContent?.trim() || "";
                                if (!level2Name) return;

                                const level2Data = {
                                    level2_name: level2Name,
                                    level3_items: []
                                };

                                // Find Level 3 items
                                const level3Items = level2Item.querySelectorAll("ul.nav-list--tertiary > li.nav-item--tertiary");

                                level3Items.forEach(level3Item => {
                                    const level3Element = level3Item.querySelector("a.nav-link--tertiary");
                                    if (!level3Element) return;

                                    const level3Name = level3Element.textContent?.trim() || "";
                                    if (level3Name) {
                                        level2Data.level3_items.push({
                                            level3_name: level3Name
                                        });
                                    }
                                });

                                itemData.level2_items.push(level2Data);
                            });

                            data.push(itemData);
                        });

                        return data;
                    }
                """)

                print(f"Successfully extracted navigation data from the page. Found {len(extracted_data)} top-level items.")

                # If no data was extracted, try a more generic approach
                if not extracted_data:
                    print("No data found with specific selectors, trying generic navigation extraction...")
                    extracted_data = page.evaluate("""
                        () => {
                            const data = [];

                            // Try to find any navigation elements
                            const navElements = document.querySelectorAll('nav, .nav, .navigation, .menu, [role="navigation"]');

                            navElements.forEach(nav => {
                                const links = nav.querySelectorAll('a');
                                links.forEach(link => {
                                    const text = link.textContent?.trim();
                                    if (text && text.length > 0 && text.length < 100) {
                                        data.push({
                                            level1_name: text,
                                            level2_items: []
                                        });
                                    }
                                });
                            });

                            return data.slice(0, 20); // Limit to first 20 items
                        }
                    """)
                    print(f"Generic extraction found {len(extracted_data)} items.")

            finally:
                # Clean up browser resources
                page.close()
                browser.close()

    except Exception as e:
        print(f"An error occurred during browser automation: {e}")
        return

    # Process the structured JSON data into the desired " > " format
    print("\n--- Generating Taxonomy ---")
    taxonomy_lines = []

    if not extracted_data:
        print("Warning: No data was extracted from the website.")
        return

    for l1_item in extracted_data:
        l1_name = l1_item.get("level1_name", "").strip().upper()

        if not l1_name or l1_name in CATEGORY_BLACKLIST:
            continue

        level2_items = l1_item.get("level2_items", [])

        # Handle cases where a top-level category has no sub-items
        if not level2_items:
            taxonomy_lines.append(l1_name)
            continue

        for l2_item in level2_items:
            l2_name = l2_item.get("level2_name", "").strip()

            if not l2_name:
                continue

            level3_items = l2_item.get("level3_items", [])

            # Handle cases where a Level 2 category has no sub-items
            if not level3_items:
                taxonomy_lines.append(f"{l1_name} > {l2_name}")
                continue

            for l3_item in level3_items:
                l3_name = l3_item.get("level3_name", "").strip()
                if l3_name:
                    taxonomy_lines.append(f"{l1_name} > {l2_name} > {l3_name}")

    if not taxonomy_lines:
        print("Warning: No taxonomy data was generated.")
        return

    print(f"\n--- Chico's Product Taxonomy ({len(taxonomy_lines)} items) ---")
    for line in sorted(taxonomy_lines):
        print(line)

    # Save to a file in the same directory as the script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    output_file = os.path.join(script_dir, "chicos_taxonomy.txt")

    try:
        with open(output_file, "w", encoding="utf-8") as f:
            for line in sorted(taxonomy_lines):
                f.write(line + "\n")
        print(f"\n--- Taxonomy saved to {output_file} ---")

        # Also save as JSON for easier programmatic access
        json_output_file = os.path.join(script_dir, "chicos_taxonomy.json")
        with open(json_output_file, "w", encoding="utf-8") as f:
            json.dump({
                "taxonomy_lines": sorted(taxonomy_lines),
                "raw_data": extracted_data,
                "total_items": len(taxonomy_lines)
            }, f, indent=2, ensure_ascii=False)
        print(f"--- Raw data and taxonomy also saved to {json_output_file} ---")

    except Exception as e:
        print(f"Error saving files: {e}")

    print(f"\n--- Session replay available at: https://browserbase.com/sessions/{session.id} ---")


if __name__ == "__main__":
    generate_chicos_taxonomy()
